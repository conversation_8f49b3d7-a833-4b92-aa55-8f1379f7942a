@import "../../styles/variables";

.navigation {
  width: 100vw;
  background-color: rgba(255, 255, 255, 0.95);
  position: sticky;
  top: 0;
  z-index: 1000;
  backdrop-filter: blur(10px);
}

// Desktop Navigation
.desktopNav {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 4rem;
  padding: 20px 2rem;
}

// Mobile Navigation
.mobileNav {
  display: none;
  position: relative;
}

.navItem {
  font-family: "Playfair Display", serif;
  font-size: 34px;
  font-weight: 400;
  color: rgb(186, 149, 173);
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  text-decoration: none;

  // Reset link styles for React Router Link components
  &:link,
  &:visited {
    color: rgb(186, 149, 173);
    text-decoration: none;
  }
}

.navItem:hover {
  color: rgb(220, 156, 198);
  transform: translateY(-2px);
}

.navItem::after {
  content: "";
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, rgb(220, 156, 198), rgb(186, 149, 173));
  transition: width 0.3s ease;
}

.navItem:hover::after {
  width: 100%;
}

// Hamburger Button
.hamburger {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 44px;
  height: 44px;
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  border-radius: $border-radius-md;
  transition: $transition-fast;
  position: relative;
  z-index: 1001;

  &:hover {
    background-color: rgba($color-primary, 0.1);
  }

  &:focus {
    outline: 2px solid $color-primary;
    outline-offset: 2px;
  }
}

.hamburgerLine {
  width: 24px;
  height: 2px;
  background-color: $color-secondary;
  margin: 2px 0;
  transition: $transition-fast;
  border-radius: 2px;
}

// Hamburger Animation
.hamburgerOpen {
  .hamburgerLine:nth-child(1) {
    transform: rotate(45deg) translate(5px, 5px);
  }

  .hamburgerLine:nth-child(2) {
    opacity: 0;
  }

  .hamburgerLine:nth-child(3) {
    transform: rotate(-45deg) translate(7px, -6px);
  }
}

// Mobile Menu
.mobileMenu {
  position: fixed;
  top: 0;
  right: -100%;
  width: 280px;
  height: 100vh;
  background-color: rgba(255, 255, 255, 0.95); // Fallback
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.98),
    rgba(248, 248, 248, 0.95)
  );
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px); // Safari support
  transition: right 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 9999;
  box-shadow: -5px 0 25px rgba(0, 0, 0, 0.15);
  border-left: 1px solid rgba($color-border-light, 0.3);
}

.mobileMenuOpen {
  right: 0;
}

.mobileMenuContent {
  display: flex;
  flex-direction: column;
  padding: 80px 2rem 2rem;
  height: 100%;
  gap: $spacing-lg;
}

.mobileNavItem {
  font-family: "Playfair Display", serif;
  font-size: $font-size-2xl;
  font-weight: 400;
  color: $color-secondary;
  text-decoration: none;
  padding: $spacing-md $spacing-sm;
  border-bottom: 1px solid rgba($color-border-light, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  border-radius: $border-radius-md;
  opacity: 1;
  transform: translateX(0);

  &:hover {
    color: $color-primary;
    background-color: rgba($color-primary, 0.1);
    transform: translateX(10px);
    box-shadow: 0 2px 8px rgba($color-primary, 0.2);
  }

  &:focus {
    outline: 2px solid $color-primary;
    outline-offset: 2px;
  }

  &:last-child {
    border-bottom: none;
  }

  // Hover effect line
  &::before {
    content: "";
    position: absolute;
    left: 0;
    top: 50%;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, $color-primary, $color-secondary);
    transition: width 0.3s ease;
    transform: translateY(-50%);
  }

  &:hover::before {
    width: 4px;
  }
}

// Simple approach - just show menu items when menu is open
.mobileMenuOpen {
  .mobileNavItem {
    opacity: 1;
    transform: translateX(0);
  }
}

// Animation keyframes
@keyframes slideInFromRight {
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

// Overlay
.overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 999;
  backdrop-filter: blur(2px);
}

// Responsive Design
@media (max-width: 1024px) {
  .desktopNav {
    gap: 2rem;
    padding: 15px 1.5rem;
  }

  .navItem {
    font-size: 28px;
  }
}

@media (max-width: $breakpoint-tablet) {
  .desktopNav {
    display: none;
  }

  .mobileNav {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding: $spacing-md 1.5rem;
  }
}

@media (max-width: $breakpoint-mobile) {
  .mobileNav {
    padding: $spacing-sm 1rem;
  }

  .mobileMenu {
    width: 100vw;
    right: -100vw;
  }

  .mobileMenuOpen {
    right: 0;
  }

  .mobileMenuContent {
    padding: 60px 1.5rem 2rem;
    gap: $spacing-md;
  }

  .mobileNavItem {
    font-size: $font-size-xl;
    padding: $spacing-sm 0;
  }
}

// Extra small devices
@media (max-width: 360px) {
  .mobileNav {
    padding: $spacing-xs 0.75rem;
  }

  .mobileMenuContent {
    padding: 50px 1rem 1rem;
  }

  .mobileNavItem {
    font-size: $font-size-lg;
  }
}

// Landscape orientation on mobile
@media (max-width: $breakpoint-tablet) and (orientation: landscape) {
  .mobileMenu {
    width: 320px;
    right: -320px;
  }

  .mobileMenuContent {
    padding: 40px 1.5rem 1rem;
    gap: $spacing-sm;
  }

  .mobileNavItem {
    font-size: $font-size-lg;
    padding: $spacing-xs 0;
  }
}

// Reduced motion preference
@media (prefers-reduced-motion: reduce) {
  .hamburgerLine,
  .mobileMenu,
  .mobileNavItem {
    transition: none;
  }

  .mobileNavItem:hover {
    transform: none;
  }
}
