@import "../../styles/_variables.scss";

.heroSection {
  width: 100%;
  padding: $spacing-4xl 2rem;
  text-align: center;
  background-color: $color-bg-white;

  .pageTitle {
    font-family: $font-primary;
    font-size: $font-size-hero-md;
    font-weight: $font-weight-extra-bold;
    color: $color-secondary;
    margin: 0;
  }
}

.checkout {
  width: 100vw;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background: $color-bg-light;
  padding-top: $spacing-4xl;

  h1 {
    font-family: $font-primary;
    font-size: $font-size-hero-md;
    font-weight: $font-weight-extra-bold;
    color: $color-secondary;
    margin: 0 0 $spacing-4xl 0;
    text-align: center;
  }

  .container {
    max-width: 1366px;
    margin: 0 auto;
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: $spacing-3xl;
    padding: 0 2rem;
    align-items: start;
  }

  section {
    background: $color-bg-white;
    border-radius: $border-radius-5xl;
    box-shadow: $shadow-sm;
    padding: $spacing-2xl;
  }

  .orderSummary {
    height: fit-content;
    align-self: start;

    h2 {
      margin-bottom: $spacing-lg;
      color: $color-text-primary;
      font-family: $font-primary;
      font-size: $font-size-3xl;
    }

    .itemsHeader {
      display: grid;
      grid-template-columns: 2fr 1fr 1fr 1fr;
      gap: $spacing-md;
      align-items: center;
      padding: $spacing-md 0;
      transition: $transition-fast;
      border-bottom: 1px solid rgba($color-border-light, 0.3);

      .productColumn {
        text-align: left;
        padding-left: $spacing-md;
      }

      .priceColumn,
      .qtyColumn,
      .totalColumn {
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }

    .itemsList {
      display: flex;
      flex-direction: column;
      gap: $spacing-sm;
      margin: 0 0 $spacing-lg 0;
      padding: 0;

      .item {
        display: grid;
        grid-template-columns: 2fr 1fr 1fr 1fr;
        gap: $spacing-md;
        align-items: center;
        padding: $spacing-md 0;
        transition: $transition-fast;
        border-bottom: 1px solid rgba($color-border-light, 0.3);

        &:last-child {
          border-bottom: none;
        }

        &:hover {
          background-color: rgba($color-bg-light, 0.2);
        }

        .productInfo {
          display: flex;
          align-items: center;
          gap: $spacing-sm;
          padding-left: $spacing-md;

          .itemImage {
            width: 50px;
            height: 50px;
            border-radius: $border-radius-md;
            object-fit: cover;
            transition: $transition-fast;
            flex-shrink: 0;

            &:hover {
              transform: scale(0.99);
            }

            &:active {
              transform: scale(0.95);
            }
          }

          .itemName {
            font-family: $font-secondary;
            font-size: $font-size-base;
            font-weight: $font-weight-medium;
            color: $color-text-primary;
          }
        }

        .priceInfo,
        .quantityInfo,
        .totalInfo {
          display: flex;
          justify-content: center;
          align-items: center;
          text-align: center;
        }

        .itemPrice,
        .itemQuantity,
        .itemTotal {
          font-family: $font-secondary;
          font-size: $font-size-base;
          color: $color-text-primary;
        }

        .itemTotal {
          font-weight: $font-weight-semibold;
          color: $color-secondary;
        }
      }
    }

    .total {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-weight: $font-weight-bold;
      font-size: $font-size-lg;
      margin-top: $spacing-lg;
      padding: $spacing-md 0;
      border-top: 2px solid $color-secondary;
      font-family: $font-primary;
      color: $color-text-primary;
    }
  }

  .shippingForm {
    h2 {
      margin-bottom: $spacing-md;
      color: $color-text-primary;
      font-family: $font-primary;
      font-size: $font-size-3xl;
    }

    .form {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: $spacing-md $spacing-lg;

      label {
        margin-bottom: $spacing-xs;
        font-weight: $font-weight-medium;
        color: $color-text-primary;
        font-family: $font-secondary;
        display: flex;
        align-items: center;
        gap: $spacing-xs;

        .required {
          color: $color-danger;
          font-weight: $font-weight-bold;
          font-size: $font-size-lg;
        }
      }

      input {
        margin-bottom: $spacing-md;
        padding: $spacing-sm $spacing-md;
        border: 1px solid $color-border-light;
        border-radius: $border-radius-md;
        font-family: $font-secondary;
        font-size: $font-size-base;
        transition: $transition-fast;
        background-color: $color-bg-white;

        &:focus {
          outline: none;
          border-color: $color-border-focus;
          box-shadow: 0 0 0 2px rgba(219, 152, 196, 0.2);
        }

        &:hover {
          border-color: $color-border-focus;
        }

        &:required {
          border-left: 3px solid $color-danger;
        }

        &:required:valid {
          border-left: 3px solid $color-primary;
        }
      }

      // Field groups for individual columns
      .fieldGroup {
        display: flex;
        flex-direction: column;

        label {
          margin-bottom: $spacing-xs;
        }

        input {
          margin-bottom: 0;
        }
      }

      // Full width fields
      .fullWidth {
        grid-column: 1 / -1;
        display: flex;
        flex-direction: column;

        label {
          margin-bottom: $spacing-xs;
        }

        input {
          margin-bottom: 0;
        }
      }

      .requiredNote {
        display: flex;
        align-items: center;
        gap: $spacing-xs;
        margin: $spacing-md 0;
        font-family: $font-secondary;
        font-size: $font-size-sm;
        color: $color-text-muted;
        font-style: italic;
        grid-column: 1 / -1;

        .required {
          color: $color-danger;
          font-weight: $font-weight-bold;
          font-size: $font-size-base;
          font-style: normal;
        }
      }

      .submitBtn {
        margin-top: $spacing-md;
        padding: $spacing-md $spacing-lg;
        background: $color-bg-button-secondary;
        color: $color-text-primary;
        border: none;
        border-radius: $border-radius-5xl;
        cursor: pointer;
        font-family: $font-primary;
        font-size: $font-size-lg;
        font-weight: $font-weight-bold;
        transition: $transition-fast;
        width: 100%;
        grid-column: 1 / -1;

        &:hover {
          background: $color-primary-hover;
          transform: scale(0.99);
        }

        &:active {
          transform: scale(0.95);
        }
      }
    }
  }

  // Responsive Design
  @media (max-width: $breakpoint-desktop) {
    .container {
      gap: $spacing-2xl;
      padding: 0 $spacing-md;
    }
  }

  @media (max-width: $breakpoint-tablet) {
    padding-top: $spacing-2xl;

    .container {
      grid-template-columns: 1fr;
      gap: $spacing-2xl;
      padding: 0 1rem;
    }

    .orderSummary {
      .itemsHeader {
        display: none; // Hide header on mobile
      }

      .itemsList {
        .item {
          grid-template-columns: 1fr;
          gap: $spacing-xs;
          padding: $spacing-sm;

          .productInfo {
            justify-content: flex-start;
            padding-bottom: $spacing-xs;
            border-bottom: 1px solid $color-border-light;

            .itemImage {
              width: 40px;
              height: 40px;
            }

            .itemName {
              font-size: $font-size-sm;
            }
          }

          .priceInfo,
          .quantityInfo,
          .totalInfo {
            justify-content: space-between;
            padding: $spacing-xs 0;
            border-top: 1px solid rgba($color-border-light, 0.5);

            &::before {
              font-family: $font-secondary;
              font-size: $font-size-xs;
              font-weight: $font-weight-medium;
              color: $color-text-muted;
            }
          }

          .priceInfo::before {
            content: "Price:";
          }

          .quantityInfo::before {
            content: "Qty:";
          }

          .totalInfo::before {
            content: "Total:";
          }
        }
      }
    }
  }

  @media (max-width: $breakpoint-mobile) {
    .heroSection {
      padding: $spacing-2xl 1rem;

      .pageTitle {
        font-size: $font-size-hero-sm;
      }
    }

    padding-top: $spacing-xl;

    .container {
      padding: 0 1rem;
    }

    section {
      padding: $spacing-lg;
    }

    .orderSummary {
      h2 {
        font-size: $font-size-xl;
      }

      .total {
        font-size: $font-size-base;
        padding: $spacing-sm 0;
      }
    }

    .shippingForm {
      h2 {
        font-size: $font-size-xl;
      }

      .form {
        grid-template-columns: 1fr;
        gap: $spacing-sm;

        .fieldGroup {
          margin-bottom: $spacing-md;
        }

        .fullWidth {
          margin-bottom: $spacing-md;
        }

        .submitBtn {
          padding: $spacing-md;
          font-size: $font-size-base;
        }
      }
    }
  }
}
