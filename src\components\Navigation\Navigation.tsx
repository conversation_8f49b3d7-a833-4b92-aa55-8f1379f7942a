import React, { useState, useEffect } from "react";
import { Link } from "react-router-dom";
import styles from "./Navigation.module.scss";
import Divider from "../Divider/Divider";

const Navigation: React.FC = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  const closeMenu = () => {
    setIsMenuOpen(false);
  };

  // Close menu on escape key
  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === "Escape" && isMenuOpen) {
        closeMenu();
      }
    };

    document.addEventListener("keydown", handleEscape);
    return () => document.removeEventListener("keydown", handleEscape);
  }, [isMenuOpen]);

  // Prevent body scroll when menu is open
  useEffect(() => {
    if (isMenuOpen) {
      document.body.style.overflow = "hidden";
    } else {
      document.body.style.overflow = "unset";
    }

    // Cleanup on unmount
    return () => {
      document.body.style.overflow = "unset";
    };
  }, [isMenuOpen]);

  return (
    <>
      <Divider />
      <nav className={styles.navigation}>
        {/* Desktop Navigation */}
        <div className={styles.desktopNav}>
          <Link to="/" className={styles.navItem}>
            HOME
          </Link>
          <Link to="/shop" className={styles.navItem}>
            SHOP
          </Link>
          <Link to="/about-us" className={styles.navItem}>
            ABOUT US
          </Link>
          <Link to="/ingredients" className={styles.navItem}>
            INGREDIENTS
          </Link>
          <Link to="/faq" className={styles.navItem}>
            FAQ
          </Link>
        </div>

        {/* Mobile Navigation */}
        <div className={styles.mobileNav}>
          {/* Hamburger Button */}
          <button
            className={`${styles.hamburger} ${
              isMenuOpen ? styles.hamburgerOpen : ""
            }`}
            onClick={toggleMenu}
            aria-label="Toggle navigation menu"
            aria-expanded={isMenuOpen}
          >
            <span className={styles.hamburgerLine}></span>
            <span className={styles.hamburgerLine}></span>
            <span className={styles.hamburgerLine}></span>
          </button>

          {/* Mobile Menu */}
          <div
            className={`${styles.mobileMenu} ${
              isMenuOpen ? styles.mobileMenuOpen : ""
            }`}
            style={{
              backgroundColor: isMenuOpen ? "red" : "blue",
              border: "3px solid yellow",
            }}
          >
            <div className={styles.mobileMenuContent}>
              <p style={{ color: "black", fontSize: "20px" }}>
                Menu State: {isMenuOpen ? "OPEN" : "CLOSED"}
              </p>
              <Link to="/" className={styles.mobileNavItem} onClick={closeMenu}>
                HOME
              </Link>
              <Link
                to="/shop"
                className={styles.mobileNavItem}
                onClick={closeMenu}
              >
                SHOP
              </Link>
              <Link
                to="/about-us"
                className={styles.mobileNavItem}
                onClick={closeMenu}
              >
                ABOUT US
              </Link>
              <Link
                to="/ingredients"
                className={styles.mobileNavItem}
                onClick={closeMenu}
              >
                INGREDIENTS
              </Link>
              <Link
                to="/faq"
                className={styles.mobileNavItem}
                onClick={closeMenu}
              >
                FAQ
              </Link>
            </div>
          </div>

          {/* Overlay */}
          {isMenuOpen && (
            <div
              className={styles.overlay}
              onClick={closeMenu}
              aria-hidden="true"
            />
          )}
        </div>
      </nav>
    </>
  );
};

export default Navigation;
